# 族谱编辑系统

一个基于 React + Node.js + PostgreSQL 的族谱管理系统。

## 项目结构

```
族谱/
├── frontend/          # React 前端应用
├── backend/           # Node.js 后端 API
├── database/          # 数据库脚本和配置
├── docs/             # 项目文档
└── README.md         # 项目说明
```

## 功能特性

### 支派世系管理
- 新增支派世系
- 修改支派世系信息
- 查询支派世系
- 删除支派世系

### 人物管理
- 新增人物信息
- 修改人物资料
- 查询人物信息
- 删除人物记录

## 技术栈

- **前端**: React.js, Axios, React Router
- **后端**: Node.js, Express.js, pg (PostgreSQL客户端)
- **数据库**: PostgreSQL
- **开发工具**: npm/yarn

## 快速开始

### 1. 数据库设置
```bash
# 创建数据库
createdb genealogy_db

# 运行数据库初始化脚本
psql -d genealogy_db -f database/init.sql
```

### 2. 后端启动
```bash
cd backend
npm install
npm start
```

### 3. 前端启动
```bash
cd frontend
npm install
npm start
```

## 数据库表结构

### 支派世系表 (branches)
- id: 主键
- name: 支派名称
- generation: 世代
- description: 描述
- parent_id: 父支派ID
- created_at: 创建时间
- updated_at: 更新时间

### 人物表 (persons)
- id: 主键
- name: 姓名
- branch_id: 所属支派ID
- birth_date: 出生日期
- death_date: 逝世日期
- biography: 生平描述
- father_id: 父亲ID
- mother_id: 母亲ID
- spouse_ids: 配偶ID列表
- created_at: 创建时间
- updated_at: 更新时间
