const express = require('express');
const router = express.Router();
const Person = require('../models/Person');

// 获取所有人物
router.get('/', async (req, res) => {
  try {
    const persons = await Person.getAll();
    res.json({
      success: true,
      data: persons
    });
  } catch (error) {
    console.error('获取人物列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取人物列表失败',
      error: error.message
    });
  }
});

// 根据ID获取人物
router.get('/:id', async (req, res) => {
  try {
    const person = await Person.getById(req.params.id);
    if (!person) {
      return res.status(404).json({
        success: false,
        message: '人物不存在'
      });
    }
    res.json({
      success: true,
      data: person
    });
  } catch (error) {
    console.error('获取人物详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取人物详情失败',
      error: error.message
    });
  }
});

// 创建新人物
router.post('/', async (req, res) => {
  try {
    const { name, branch_id, birth_date, death_date, biography, father_id, mother_id, spouse_ids, gender } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        message: '人物姓名为必填项'
      });
    }

    const newPerson = await Person.create({
      name,
      branch_id,
      birth_date,
      death_date,
      biography,
      father_id,
      mother_id,
      spouse_ids,
      gender
    });

    res.status(201).json({
      success: true,
      data: newPerson,
      message: '人物创建成功'
    });
  } catch (error) {
    console.error('创建人物失败:', error);
    res.status(500).json({
      success: false,
      message: '创建人物失败',
      error: error.message
    });
  }
});

// 更新人物
router.put('/:id', async (req, res) => {
  try {
    const { name, branch_id, birth_date, death_date, biography, father_id, mother_id, spouse_ids, gender } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        message: '人物姓名为必填项'
      });
    }

    const updatedPerson = await Person.update(req.params.id, {
      name,
      branch_id,
      birth_date,
      death_date,
      biography,
      father_id,
      mother_id,
      spouse_ids,
      gender
    });

    if (!updatedPerson) {
      return res.status(404).json({
        success: false,
        message: '人物不存在'
      });
    }

    res.json({
      success: true,
      data: updatedPerson,
      message: '人物更新成功'
    });
  } catch (error) {
    console.error('更新人物失败:', error);
    res.status(500).json({
      success: false,
      message: '更新人物失败',
      error: error.message
    });
  }
});

// 删除人物
router.delete('/:id', async (req, res) => {
  try {
    const deletedPerson = await Person.delete(req.params.id);
    
    if (!deletedPerson) {
      return res.status(404).json({
        success: false,
        message: '人物不存在'
      });
    }

    res.json({
      success: true,
      data: deletedPerson,
      message: '人物删除成功'
    });
  } catch (error) {
    console.error('删除人物失败:', error);
    res.status(500).json({
      success: false,
      message: '删除人物失败',
      error: error.message
    });
  }
});

// 搜索人物
router.get('/search/:term', async (req, res) => {
  try {
    const persons = await Person.search(req.params.term);
    res.json({
      success: true,
      data: persons
    });
  } catch (error) {
    console.error('搜索人物失败:', error);
    res.status(500).json({
      success: false,
      message: '搜索人物失败',
      error: error.message
    });
  }
});

module.exports = router;
