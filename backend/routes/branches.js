const express = require('express');
const router = express.Router();
const Branch = require('../models/Branch');

// 获取所有支派
router.get('/', async (req, res) => {
  try {
    const branches = await Branch.getAll();
    res.json({
      success: true,
      data: branches
    });
  } catch (error) {
    console.error('获取支派列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取支派列表失败',
      error: error.message
    });
  }
});

// 根据ID获取支派
router.get('/:id', async (req, res) => {
  try {
    const branch = await Branch.getById(req.params.id);
    if (!branch) {
      return res.status(404).json({
        success: false,
        message: '支派不存在'
      });
    }
    res.json({
      success: true,
      data: branch
    });
  } catch (error) {
    console.error('获取支派详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取支派详情失败',
      error: error.message
    });
  }
});

// 创建新支派
router.post('/', async (req, res) => {
  try {
    const { name, generation, description, parent_id } = req.body;
    
    if (!name || !generation) {
      return res.status(400).json({
        success: false,
        message: '支派名称和世代为必填项'
      });
    }

    const newBranch = await Branch.create({
      name,
      generation,
      description,
      parent_id
    });

    res.status(201).json({
      success: true,
      data: newBranch,
      message: '支派创建成功'
    });
  } catch (error) {
    console.error('创建支派失败:', error);
    res.status(500).json({
      success: false,
      message: '创建支派失败',
      error: error.message
    });
  }
});

// 更新支派
router.put('/:id', async (req, res) => {
  try {
    const { name, generation, description, parent_id } = req.body;
    
    if (!name || !generation) {
      return res.status(400).json({
        success: false,
        message: '支派名称和世代为必填项'
      });
    }

    const updatedBranch = await Branch.update(req.params.id, {
      name,
      generation,
      description,
      parent_id
    });

    if (!updatedBranch) {
      return res.status(404).json({
        success: false,
        message: '支派不存在'
      });
    }

    res.json({
      success: true,
      data: updatedBranch,
      message: '支派更新成功'
    });
  } catch (error) {
    console.error('更新支派失败:', error);
    res.status(500).json({
      success: false,
      message: '更新支派失败',
      error: error.message
    });
  }
});

// 删除支派
router.delete('/:id', async (req, res) => {
  try {
    const deletedBranch = await Branch.delete(req.params.id);
    
    if (!deletedBranch) {
      return res.status(404).json({
        success: false,
        message: '支派不存在'
      });
    }

    res.json({
      success: true,
      data: deletedBranch,
      message: '支派删除成功'
    });
  } catch (error) {
    console.error('删除支派失败:', error);
    res.status(500).json({
      success: false,
      message: '删除支派失败',
      error: error.message
    });
  }
});

// 获取支派的子支派
router.get('/:id/children', async (req, res) => {
  try {
    const children = await Branch.getChildren(req.params.id);
    res.json({
      success: true,
      data: children
    });
  } catch (error) {
    console.error('获取子支派失败:', error);
    res.status(500).json({
      success: false,
      message: '获取子支派失败',
      error: error.message
    });
  }
});

module.exports = router;
