const pool = require('../config/database');

class Branch {
  // 获取所有支派
  static async getAll() {
    const query = `
      SELECT b.*, 
             pb.name as parent_name,
             COUNT(p.id) as person_count
      FROM branches b
      LEFT JOIN branches pb ON b.parent_id = pb.id
      LEFT JOIN persons p ON b.id = p.branch_id
      GROUP BY b.id, pb.name
      ORDER BY b.generation, b.name
    `;
    const result = await pool.query(query);
    return result.rows;
  }

  // 根据ID获取支派
  static async getById(id) {
    const query = `
      SELECT b.*, 
             pb.name as parent_name
      FROM branches b
      LEFT JOIN branches pb ON b.parent_id = pb.id
      WHERE b.id = $1
    `;
    const result = await pool.query(query, [id]);
    return result.rows[0];
  }

  // 创建新支派
  static async create(branchData) {
    const { name, generation, description, parent_id } = branchData;
    const query = `
      INSERT INTO branches (name, generation, description, parent_id)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `;
    const result = await pool.query(query, [name, generation, description, parent_id]);
    return result.rows[0];
  }

  // 更新支派
  static async update(id, branchData) {
    const { name, generation, description, parent_id } = branchData;
    const query = `
      UPDATE branches 
      SET name = $1, generation = $2, description = $3, parent_id = $4
      WHERE id = $5
      RETURNING *
    `;
    const result = await pool.query(query, [name, generation, description, parent_id, id]);
    return result.rows[0];
  }

  // 删除支派
  static async delete(id) {
    const query = 'DELETE FROM branches WHERE id = $1 RETURNING *';
    const result = await pool.query(query, [id]);
    return result.rows[0];
  }

  // 获取支派的子支派
  static async getChildren(parentId) {
    const query = `
      SELECT * FROM branches 
      WHERE parent_id = $1 
      ORDER BY generation, name
    `;
    const result = await pool.query(query, [parentId]);
    return result.rows;
  }
}

module.exports = Branch;
