const pool = require('../config/database');

class Person {
  // 获取所有人物
  static async getAll() {
    const query = `
      SELECT p.*, 
             b.name as branch_name,
             f.name as father_name,
             m.name as mother_name
      FROM persons p
      LEFT JOIN branches b ON p.branch_id = b.id
      LEFT JOIN persons f ON p.father_id = f.id
      LEFT JOIN persons m ON p.mother_id = m.id
      ORDER BY p.name
    `;
    const result = await pool.query(query);
    return result.rows;
  }

  // 根据ID获取人物
  static async getById(id) {
    const query = `
      SELECT p.*, 
             b.name as branch_name,
             f.name as father_name,
             m.name as mother_name
      FROM persons p
      LEFT JOIN branches b ON p.branch_id = b.id
      LEFT JOIN persons f ON p.father_id = f.id
      LEFT JOIN persons m ON p.mother_id = m.id
      WHERE p.id = $1
    `;
    const result = await pool.query(query, [id]);
    return result.rows[0];
  }

  // 根据支派ID获取人物
  static async getByBranchId(branchId) {
    const query = `
      SELECT p.*, 
             f.name as father_name,
             m.name as mother_name
      FROM persons p
      LEFT JOIN persons f ON p.father_id = f.id
      LEFT JOIN persons m ON p.mother_id = m.id
      WHERE p.branch_id = $1
      ORDER BY p.name
    `;
    const result = await pool.query(query, [branchId]);
    return result.rows;
  }

  // 创建新人物
  static async create(personData) {
    const { 
      name, 
      branch_id, 
      birth_date, 
      death_date, 
      biography, 
      father_id, 
      mother_id, 
      spouse_ids, 
      gender 
    } = personData;
    
    const query = `
      INSERT INTO persons (name, branch_id, birth_date, death_date, biography, father_id, mother_id, spouse_ids, gender)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `;
    const result = await pool.query(query, [
      name, branch_id, birth_date, death_date, biography, father_id, mother_id, spouse_ids, gender
    ]);
    return result.rows[0];
  }

  // 更新人物
  static async update(id, personData) {
    const { 
      name, 
      branch_id, 
      birth_date, 
      death_date, 
      biography, 
      father_id, 
      mother_id, 
      spouse_ids, 
      gender 
    } = personData;
    
    const query = `
      UPDATE persons 
      SET name = $1, branch_id = $2, birth_date = $3, death_date = $4, 
          biography = $5, father_id = $6, mother_id = $7, spouse_ids = $8, gender = $9
      WHERE id = $10
      RETURNING *
    `;
    const result = await pool.query(query, [
      name, branch_id, birth_date, death_date, biography, father_id, mother_id, spouse_ids, gender, id
    ]);
    return result.rows[0];
  }

  // 删除人物
  static async delete(id) {
    const query = 'DELETE FROM persons WHERE id = $1 RETURNING *';
    const result = await pool.query(query, [id]);
    return result.rows[0];
  }

  // 搜索人物
  static async search(searchTerm) {
    const query = `
      SELECT p.*, 
             b.name as branch_name,
             f.name as father_name,
             m.name as mother_name
      FROM persons p
      LEFT JOIN branches b ON p.branch_id = b.id
      LEFT JOIN persons f ON p.father_id = f.id
      LEFT JOIN persons m ON p.mother_id = m.id
      WHERE p.name ILIKE $1 OR p.biography ILIKE $1
      ORDER BY p.name
    `;
    const result = await pool.query(query, [`%${searchTerm}%`]);
    return result.rows;
  }
}

module.exports = Person;
