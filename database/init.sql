-- 族谱数据库初始化脚本

-- 创建数据库扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 支派世系表
CREATE TABLE IF NOT EXISTS branches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    generation INTEGER NOT NULL DEFAULT 1,
    description TEXT,
    parent_id UUID REFERENCES branches(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 人物表
CREATE TABLE IF NOT EXISTS persons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    branch_id UUID REFERENCES branches(id) ON DELETE SET NULL,
    birth_date DATE,
    death_date DATE,
    biography TEXT,
    father_id UUID REFERENCES persons(id) ON DELETE SET NULL,
    mother_id UUID REFERENCES persons(id) ON DELETE SET NULL,
    spouse_ids UUID[],
    gender VARCHAR(10) CHECK (gender IN ('male', 'female', 'unknown')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_branches_parent_id ON branches(parent_id);
CREATE INDEX IF NOT EXISTS idx_branches_generation ON branches(generation);
CREATE INDEX IF NOT EXISTS idx_persons_branch_id ON persons(branch_id);
CREATE INDEX IF NOT EXISTS idx_persons_father_id ON persons(father_id);
CREATE INDEX IF NOT EXISTS idx_persons_mother_id ON persons(mother_id);
CREATE INDEX IF NOT EXISTS idx_persons_name ON persons(name);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为表添加更新时间触发器
CREATE TRIGGER update_branches_updated_at 
    BEFORE UPDATE ON branches 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_persons_updated_at 
    BEFORE UPDATE ON persons 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入示例数据
INSERT INTO branches (name, generation, description) VALUES 
('主支', 1, '族谱主支派'),
('长房', 2, '长房支派'),
('二房', 2, '二房支派');

-- 获取支派ID用于插入人物数据
DO $$
DECLARE
    main_branch_id UUID;
    first_branch_id UUID;
    second_branch_id UUID;
BEGIN
    SELECT id INTO main_branch_id FROM branches WHERE name = '主支';
    SELECT id INTO first_branch_id FROM branches WHERE name = '长房';
    SELECT id INTO second_branch_id FROM branches WHERE name = '二房';
    
    -- 更新支派的父子关系
    UPDATE branches SET parent_id = main_branch_id WHERE name IN ('长房', '二房');
    
    -- 插入示例人物数据
    INSERT INTO persons (name, branch_id, gender, biography) VALUES 
    ('张三', main_branch_id, 'male', '族谱始祖，生于明朝'),
    ('张四', first_branch_id, 'male', '长房长子'),
    ('张五', second_branch_id, 'male', '二房长子');
END $$;
